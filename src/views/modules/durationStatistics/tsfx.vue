<!--
 * @Description:
-->
<template>
  <div class="duration-statistics" @click="handleGlobalClick">
    <!-- 顶部筛选区域 -->
    <div class="filter-section">
      <el-form inline>
        <el-form-item>
          <el-radio-group
              v-model="searchForm.timeType"
              @change="handleTimeTypeChange"
              style="margin-right: 10px"
          >
            <el-radio :label="1">单月</el-radio>
            <el-radio :label="2">时间段</el-radio>
          </el-radio-group>
          <el-date-picker
              v-show="searchForm.timeType == 1"
              v-model="searchForm.singleMonth"
              type="month"
              placeholder="选择月份"
              value-format="yyyyMM"
              class="date-picker"
              @change="getList"
          >
          </el-date-picker>

          <el-date-picker
              v-if="searchForm.timeType == 2"
              v-model="searchForm.monthrange"
              type="monthrange"
              range-separator="至"
              value-format="yyyyMM"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="getList"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" icon="el-icon-download" @click="download" plain>生成excel</el-button>
          <el-button type="warning" @click="toggleDataSource" plain>
            {{ useFixedData ? '切换到API数据' : '切换到固定数据' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <!-- 新增标题区域 -->
      <div class="table-title">
        <div class="title-decoration"></div>
        <h2>犯罪态势分析</h2>
      </div>
      <div style="margin-top: 5px;margin-bottom: 15px">
        <el-checkbox v-model="tableCellStyleType.max" :true-label="1" :false-label="0">最大值</el-checkbox>
        <el-checkbox v-model="tableCellStyleType.min" :true-label="1" :false-label="0">最小值</el-checkbox>
        <el-checkbox v-model="tableCellStyleType.zero" :true-label="1" :false-label="0">0值</el-checkbox>
<!--        <el-button style="margin-left: 20px" plain type="primary" @click="caseStatuteOfLimitations($event)">案件时效</el-button>-->
<!--        <el-button type="primary" plain @click="focusPerformance($event)">案件绩效</el-button>-->
<!--        <el-button type="primary" plain @click="focusWarningColumn($event)">案件质量</el-button>-->
      </div>
      <!-- :summary-method="getSummaries" -->
      <!-- 表格区域 -->
      <el-table
          :data="tableData"
          border
          style="width: 100%"
          :span-method="arraySpanMethod"
          v-loading="loading"
          :header-cell-style="getHeaderCellStyle"
          :header-cell-class-name="getHeaderCellClassName"
          :cell-style="cellStyle"
          ref="dataTable"
          @header-click="handleHeaderClick"
      >
        <!-- 罪名列 -->
        <el-table-column
            prop="罪名"
            label="罪名"
            width="150"
            fixed="left"
            align="center"
        >
          <template slot-scope="scope">
            <span>
              {{ scope.row.罪名 }}
            </span>
          </template>
        </el-table-column>
        <!-- 数据列 -->
        <el-table-column label="2023年数量" prop="2023年数量" width="120" align="center">
          <template slot-scope="scope">
            <span class="cell-content" @click="handleCellClick(scope.row, '2023年数量', scope.$index)">
              {{ scope.row['2023年数量'] || '-' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="2024年数量" prop="2024年数量" width="120" align="center">
          <template slot-scope="scope">
            <span class="cell-content" @click="handleCellClick(scope.row, '2024年数量', scope.$index)">
              {{ scope.row['2024年数量'] || '-' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="2024年1-5月数量" prop="2024年1-5月数量" width="140" align="center">
          <template slot-scope="scope">
            <span class="cell-content" @click="handleCellClick(scope.row, '2024年1-5月数量', scope.$index)">
              {{ scope.row['2024年1-5月数量'] || '-' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="2025年1-5月数量" prop="2025年1-5月数量" width="140" align="center">
          <template slot-scope="scope">
            <span class="cell-content" @click="handleCellClick(scope.row, '2025年1-5月数量', scope.$index)">
              {{ scope.row['2025年1-5月数量'] || '-' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="2024年-2023年增长数" prop="2024年-2023年增长数" width="160" align="center">
          <template slot-scope="scope">
            <span class="cell-content" @click="handleCellClick(scope.row, '2024年-2023年增长数', scope.$index)">
              <span :class="getGrowthClass(scope.row['2024年-2023年增长数'])">
                {{ scope.row['2024年-2023年增长数'] || '-' }}
              </span>
            </span>
          </template>
        </el-table-column>

        <el-table-column label="2024年-2023年增长率" prop="2024年-2023年增长率" width="160" align="center">
          <template slot-scope="scope">
            <span class="cell-content" @click="handleCellClick(scope.row, '2024年-2023年增长率', scope.$index)">
              <span :class="getGrowthClass(scope.row['2024年-2023年增长率'])">
                {{ formatPercentage(scope.row['2024年-2023年增长率']) }}
              </span>
            </span>
          </template>
        </el-table-column>

        <el-table-column label="2025年1-5月增长数" prop="2025年1-5月增长数" width="150" align="center">
          <template slot-scope="scope">
            <span class="cell-content" @click="handleCellClick(scope.row, '2025年1-5月增长数', scope.$index)">
              <span :class="getGrowthClass(scope.row['2025年1-5月增长数'])">
                {{ scope.row['2025年1-5月增长数'] || '-' }}
              </span>
            </span>
          </template>
        </el-table-column>

        <el-table-column label="2025年1-5月增长率" prop="2025年1-5月增长率" width="150" align="center">
          <template slot-scope="scope">
            <span class="cell-content" @click="handleCellClick(scope.row, '2025年1-5月增长率', scope.$index)">
              <span :class="getGrowthClass(scope.row['2025年1-5月增长率'])">
                {{ formatPercentage(scope.row['2025年1-5月增长率']) }}
              </span>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
import {
  getDepartmentAndUser,
  getStatisticsData,
  getTableHeader,
  getDetailTableData, getDetailTableLeader
} from "@/api/api/durationStatistics"
// import * as XLSX from 'xlsx';
import * as XLSX from 'sheetjs-style'
import { calculateColWidth, calculateWidth, wsInit } from "@/utils/excelUtils"
import { rulePageList, warningDepartmentAndUser, warninglistByFilter, warningPageList } from "@/api/api/warningInfo"
import WarningDetailDialog from "@/views/modules/warningInfo/detailDialog.vue"
import { listCbrpcjgtj } from "@/api/api/qualityEvaluation"
import { formatEndTime, formatStartTime } from "@/views/modules/durationStatistics/timeUtils"
import { bakPjdcData } from "@/views/modules/durationStatistics/bakJson"
import ajzlpjdcDialog from "@/views/modules/ajzlpjdc/ajzlpjdcDialog.vue"
import AllDetailDialog from "@/views/modules/warningInfo/allDetailDialog.vue"
// import { departmentAndUserList, statisticsData, tableHeader } from "@/views/modules/durationStatistics/bakJson"

export default {
  name: "DurationStatistics",
  components: { AllDetailDialog, WarningDetailDialog,ajzlpjdcDialog },
  data() {
    return {
      detailLoading: false,
      WarningDialogVisible: false,
      allWarningDialogVisible: false,
      pjdcDialogVisible: false,
      ruleDetailLoading: false,
      ruleDialogVisible: false,
      ruleTableData: [],
      tableCellStyleType: {
        max: 1,
        min: 1,
        zero: 1
      },
      listQuery: {
        page: 1,
        rows: 5,
        entity: {
          cbrgh: "",
          startTime: "",
          endTime: ""
        }
      },
      ruleListQuery: {
        pageNo: 1,
        rows: 10,
        sort: "",
        order: "",
        filters: "",
        searchFilters: ""
      },
      ruleTotal: 0,
      loading: true,
      searchForm: {
        timeType: 2, // 1: 单月, 2: 时间段 - 默认改为时间段以显示整年
        singleMonth: "202501",
        monthrange: ["202501", "202512"]
      },
      tableData: [],
      tableHeader: [],
      columns: [],
      dialogVisible: false,
      rowCbrgh: '',
      currentCell: {
        department: "",
        cbr: "",
        columnTitle: "",
        value: ""
      },
      detailTableData: [],
      detailTableAllData: [],
      allWarningActiveName: "",
      WarningActiveName: "",
      pjdcActiveName: "",
      total: 0,
      cols: [
        { label: "部门受案号", prop: "BMSAH" },
        { label: "案件名称", prop: "AJMC" },
        { label: "嫌疑人姓名", prop: "XYRXM" },
        { label: "受理日期", prop: "SLRQ_DATE" },
        { label: "审结日期", prop: "SJRQ_DATE" },
        { label: "承办单位", prop: "CBDW_MC" },
        { label: "承办部门", prop: "CBBM_MC" },
        { label: "承办检察官", prop: "CBR" },
        { label: "承办检察官编码", prop: "CBRGH" },
        { label: "办案时长", prop: "DURATION" }
      ],
      departmentAndUserList: [],
      statisticsData: [],
      focusedColumn: '', // 新增：用于跟踪当前聚焦的列
      nowTime: '',
      warningType: '',
      rowData: {},
      allWarningrowData: {},
      pjdcRowData: {},
      activeName: '',
      cbrpcjgtjData: {
        dwbm: "330183"
      },
      yldData:{
        slgsj: "0",
        slbbj: "0",
        bjgsjs: "0",
        wjgsjs: "0"
      },
      // 预警内容弹框相关数据
      warningContentDialogVisible: false,
      warningContentLoading: false,
      warningContentList: [],
      warningContentTotal: 0,
      currentWarningRule: {},
      warningContentQuery: {
        pageNo: 1,
        pageSize: 10
      },
      expandedWarningRows: [], // 预警内容表格展开状态
      // 固定的犯罪数据
      crimeStatisticsData: [
        {
          "罪名": "危险驾驶罪",
          "2023年数量": 307,
          "2024年数量": 233,
          "2024年1-5月数量": 92,
          "2025年1-5月数量": 102,
          "2024年-2023年增长数": -74,
          "2024年-2023年增长率": -24.10423,
          "2025年1-5月增长数": 10,
          "2025年1-5月增长率": 10.86957
        },
        {
          "罪名": "盗窃罪",
          "2023年数量": 137,
          "2024年数量": 164,
          "2024年1-5月数量": 80,
          "2025年1-5月数量": 66,
          "2024年-2023年增长数": 27,
          "2024年-2023年增长率": 19.70803,
          "2025年1-5月增长数": -14,
          "2025年1-5月增长率": -17.5
        },
        {
          "罪名": "诈骗罪",
          "2023年数量": 112,
          "2024年数量": 120,
          "2024年1-5月数量": 61,
          "2025年1-5月数量": 49,
          "2024年-2023年增长数": 8,
          "2024年-2023年增长率": 7.14286,
          "2025年1-5月增长数": -12,
          "2025年1-5月增长率": -19.67213
        }
      ],
      useFixedData: true // 控制是否使用固定数据
    };
  },
  watch: {
    activeName(activeName) {
      if (activeName) {
        // 每次切换详情的时候，重置分页
        this.listQuery.page = 1;
        // 获取详情数据
        this.getDetailTableData();
      }
    }
  },

  async mounted() {
    // 设置为今年：从1月到12月
    const currentYear = new Date().getFullYear().toString()
    this.nowTime = currentYear + "12" // 今年12月
    this.nowTimeLast = currentYear + "01" // 今年1月
    this.searchForm.singleMonth = this.nowTime
    this.searchForm.monthrange = [this.nowTimeLast, this.nowTime] // 今年1月到12月
    document.title = "犯罪态势分析";
    // 获取字段
    await this.getTableHeader();
    // 获取犯罪统计数据
    await this.getList();
  },
  methods: {
    smoothScroll(element, target) {
      const start = element.scrollLeft
      const change = target - start
      const duration = 300
      let startTime = null

      const animation = (currentTime) => {
        if (startTime === null) startTime = currentTime
        const timeElapsed = currentTime - startTime
        const progress = Math.min(timeElapsed / duration, 1)

        const run = progress => progress < 0.5
          ? 2 * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 2) / 2

        element.scrollLeft = start + change * run(progress)

        if (timeElapsed < duration) {
          requestAnimationFrame(animation)
        }
      }

      requestAnimationFrame(animation)
    },
    async downloadDetail() {

      const query = this.listQuery
      query.rows = 999999
      const allData = await getDetailTableData(
          this.activeName,
          query
      );
      this.detailTableAllData = allData.data.list;

      if (!this.detailTableAllData || this.detailTableAllData.length === 0) {
        this.$message.warning('没有可导出的数据');
        return;
      }

      // 处理数据（添加办案时长）
      const processedData = this.detailTableAllData;

      // 准备表头和数据
      const headers = this.cols.map(item => item.label);
      const data = processedData.map(item => {
        return this.cols.map(field => {
          const value = item[field.prop];
          // 处理空值
          if (value === null || value === undefined) return '';
          return value;
        });
      });

      // 合并表头和数据
      const excelData = [headers, ...data];

      // 创建工作表
      const ws = wsInit(excelData, 1)

      // 设置列宽
      ws['!cols'] = this.cols.map(field => ({
        wch: calculateColWidth(processedData, field)
      }));

      // 创建工作簿并导出
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '案件列表');

      // 生成文件名（当前日期）
      const today = new Date();
      const dateStr = `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
      XLSX.writeFile(wb, `案件数据_${dateStr}.xlsx`);
    },
    download() {
      this.$confirm(`确定将当前的数据生成一个Excel?`, '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const headerStructure = this.tableHeader
        const departmentData = this.departmentAndUserList
        const cbrghData = this.statisticsData

        const getLastDayOfMonth = (year, month_1_indexed) => {
          return new Date(year, month_1_indexed, 0).getDate();
        };

        let titleDateStr = "";
        const getYear = yyyyMM => yyyyMM.substring(0, 4);
        const getMonth = yyyyMM => parseInt(yyyyMM.substring(4, 6));

        if (this.searchForm.timeType === 1 && this.searchForm.singleMonth) {
          const year = getYear(this.searchForm.singleMonth);
          const month = getMonth(this.searchForm.singleMonth);
          const lastDay = getLastDayOfMonth(parseInt(year), month);
          titleDateStr = `${year}.${month}.1-${lastDay}`;
        } else if (this.searchForm.timeType === 2 && this.searchForm.monthrange && this.searchForm.monthrange.length === 2) {
          const startYear = getYear(this.searchForm.monthrange[0]);
          const startMonth = getMonth(this.searchForm.monthrange[0]);
          const startDateDisplay = `${startYear}.${startMonth}.1`;

          const endYear = getYear(this.searchForm.monthrange[1]);
          const endMonth = getMonth(this.searchForm.monthrange[1]);
          const endLastDay = getLastDayOfMonth(parseInt(endYear), endMonth);
          const endDateDisplay = `${endYear}.${endMonth}.${endLastDay}`;

          if (startYear === endYear) {
            titleDateStr = `${startYear}.${startMonth}.1-${endMonth}.${endLastDay}`;
          } else {
            titleDateStr = `${startDateDisplay}-${endDateDisplay}`;
          }
        }
        const mainTitle = `一审公诉案件办案时长情况${titleDateStr}`;

        const headerRow1_actual = ['部门', '员额'];
        const headerRow2_actual = ['部门', '员额'];
        const mergesForHeadersAndData = [];
        let colIndex = 2;

        headerStructure.forEach(item => {
          if (item.subTitle && item.subTitle.length) {
            headerRow1_actual.push(item.title);
            for (let i = 0; i < item.subTitle.length - 1; i++) {
              headerRow1_actual.push('');
            }
            item.subTitle.forEach(sub => {
              headerRow2_actual.push(sub.title);
            });
            mergesForHeadersAndData.push({ // Relative to header block start (row 2)
              s: { r: 0, c: colIndex },
              e: { r: 0, c: colIndex + item.subTitle.length - 1 },
            });
            colIndex += item.subTitle.length;
          } else {
            headerRow1_actual.push(item.title);
            headerRow2_actual.push('');
            mergesForHeadersAndData.push({ // Relative to header block start (row 2)
              s: { r: 0, c: colIndex },
              e: { r: 1, c: colIndex },
            });
            colIndex += 1;
          }
        });
        mergesForHeadersAndData.unshift( // Relative to header block start (row 2)
          { s: { r: 0, c: 1 }, e: { r: 1, c: 1 } }, // 员额
          { s: { r: 0, c: 0 }, e: { r: 1, c: 0 } }  // 部门
        );

        const excelDataRows = [];
        departmentData.forEach(dept => {
          dept.personList.forEach((person, idx) => {
            const row = [];
            row.push(idx === 0 ? dept.name : '');
            row.push(person.cbr);
            const personData = cbrghData[person.cbrgh] || [];
            const numericData = personData.map(val => val === null || val === undefined || val === '' ? '' : (isNaN(Number(val)) ? val : Number(val)));
            row.push(...numericData);
            excelDataRows.push(row);
          });
          if (dept.personList.length > 1) {
            mergesForHeadersAndData.push({ // Relative to data block start (row 4)
              s: { r: excelDataRows.length - dept.personList.length + 2, c: 0 }, // +2 for header rows offset later
              e: { r: excelDataRows.length - 1 + 2, c: 0 },
            });
          }
        });

        const totalData = cbrghData["total"] || [];
        const numericTotalData = totalData.map(val => val === null || val === undefined || val === '' ? '' : (isNaN(Number(val)) ? val : Number(val)));
        const totalRow_data = ["总计", ""];
        totalRow_data.push(...numericTotalData);
        excelDataRows.push(totalRow_data);

        mergesForHeadersAndData.push({ // Relative to data block start (row 4) for total row
          s: { r: excelDataRows.length - 1 + 2, c: 0 }, // +2 for header rows offset later
          e: { r: excelDataRows.length - 1 + 2, c: 1 },
        });

        const titleVisualRow1 = [mainTitle].concat(Array(colIndex - 1).fill(''));
        const titleVisualRow2 = Array(colIndex).fill('');
        const notesString = `注：1.合计数中包括院领导受理报捕数、受理公诉数；院领导办结数以及未结数；2.省院九部经与一部和案管办协商沟通，明确目前省院通报的办案时长按照最高检统计口径不包括未检案件；院领导受理报捕数（${this.yldData.slbbj || '0'}件）、受理公诉数（${this.yldData.slgsj || '0'}件）；院领导办结数（${this.yldData.bjgsjs || '0'}件）以及未结数（${this.yldData.wjgsjs || '0'}件）。`;
        const notesRowData = [notesString].concat(Array(colIndex - 1).fill(''));

        const completeSheetData = [
          titleVisualRow1, titleVisualRow2,
          headerRow1_actual, headerRow2_actual,
          ...excelDataRows,
          notesRowData
        ];

        const ws = XLSX.utils.aoa_to_sheet(completeSheetData);

        const finalMerges = [
          { s: { r: 0, c: 0 }, e: { r: 1, c: colIndex - 1 } }, // Main title
          ...mergesForHeadersAndData.map(m => ({ // Shift header and data merges
            s: { r: m.s.r + 2, c: m.s.c }, // Base offset by 2 for title rows
            e: { r: m.e.r + 2, c: m.e.c }
          })),
          { s: { r: completeSheetData.length - 1, c: 0 }, e: { r: completeSheetData.length - 1, c: colIndex - 1 } } // Notes
        ];
        ws['!merges'] = finalMerges;

        ws['!cols'] = headerRow1_actual.map((title, idx) => {
            let colWidth = calculateWidth(title);
            if (headerRow2_actual[idx] && calculateWidth(headerRow2_actual[idx]) > colWidth) {
                colWidth = calculateWidth(headerRow2_actual[idx]);
            }
            if (idx >= 2) colWidth = Math.max(colWidth, 10);
            return { wch: colWidth };
        });
        ws['!cols'][0] = { wch: 15 }; ws['!cols'][1] = { wch: 12 };

        ws['!rows'] = [];
        ws['!rows'][0] = { hpx: 30 }; ws['!rows'][1] = { hpx: 30 }; // Title
        ws['!rows'][2] = { hpx: 30 }; ws['!rows'][3] = { hpx: 30 }; // Headers
        for (let i = 0; i < excelDataRows.length; i++) {
          ws['!rows'][4 + i] = { hpx: 28 }; // Data rows
        }
        ws['!rows'][completeSheetData.length - 1] = { hpx: 60 }; // Notes

        // --- STYLING ---
        const borderAll = {
          top: { style: "thin", color: { auto: 1 } }, bottom: { style: "thin", color: { auto: 1 } },
          left: { style: "thin", color: { auto: 1 } }, right: { style: "thin", color: { auto: 1 } }
        };
        const mainTitleStyle = {
          font: { sz: 16, color: { rgb: "C00000" }, bold: true, name: '宋体' },
          alignment: { vertical: "center", horizontal: "center" }
        };
        if (ws['A1']) ws['A1'].s = mainTitleStyle; else ws['A1'] = { t:'s', v: mainTitle, s: mainTitleStyle };


        const headerCellStyleBase = {
          fill: { fgColor: { rgb: "409EFF" } }, font: { bold: true, color: { rgb: "FFFFFF" }, name: '宋体' },
          alignment: { vertical: "center", horizontal: "center" ,wrapText: true}, border: borderAll
        };
        for (let r_idx = 2; r_idx <= 3; ++r_idx) { // Header rows are at index 2 and 3 of completeSheetData
          for (let c_idx = 0; c_idx < colIndex; ++c_idx) {
            const cellAddress = XLSX.utils.encode_cell({ r: r_idx, c: c_idx });
            if (ws[cellAddress]) ws[cellAddress].s = headerCellStyleBase;
            else ws[cellAddress] = { t:'s', v: completeSheetData[r_idx][c_idx] || "", s: headerCellStyleBase };
          }
        }

        // Data and Total rows styling (from sheet row index 4 up to before notes row)
        const dataStartIndex = 4;
        const dataEndIndex = completeSheetData.length - 2; // -1 for notes, -1 for 0-based vs length

        for (let r_idx = dataStartIndex; r_idx <= dataEndIndex; ++r_idx) {
          for (let c_idx = 0; c_idx < colIndex; ++c_idx) {
            const cellAddress = XLSX.utils.encode_cell({ r: r_idx, c: c_idx });
            const cellValue = completeSheetData[r_idx][c_idx];
            let cellStyle = { font: { name: '宋体' }, border: borderAll, alignment: {} };

            if (c_idx < 2) { // First two columns ("部门", "员额" data)
              cellStyle.alignment = { vertical: "center", horizontal: "center" };
            } else { // Other data columns
              cellStyle.alignment = { vertical: "center", horizontal: "center" };
            }

            if (ws[cellAddress]) {
              ws[cellAddress].s = { ...ws[cellAddress].s, ...cellStyle };
              if (typeof cellValue === 'number' && !isNaN(cellValue)) {
                ws[cellAddress].t = 'n';
              }
            } else {
              ws[cellAddress] = {
                v: cellValue,
                t: (typeof cellValue === 'number' && !isNaN(cellValue)) ? 'n' : 's',
                s: cellStyle
              };
            }
          }
        }

        // Notes row styling
        const notesStyle = { font: { name: '宋体' }, alignment: { vertical: "top", horizontal: "left", wrapText: true } };
        const notesCellAddress = XLSX.utils.encode_cell({ r: completeSheetData.length - 1, c: 0 });
        if(ws[notesCellAddress]) ws[notesCellAddress].s = notesStyle;
        else ws[notesCellAddress] = {t:'s', v: notesString, s: notesStyle };


        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
        const today = new Date();
        const dateStr = `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
        const excelFileName = `一审公诉案件办案时长情况${titleDateStr}.xlsx`;
        XLSX.writeFile(wb, excelFileName);

      }).catch((err)=>{
        console.error('Excel generation failed:', err);
      });
    },
    async getTableHeader() {
      if (this.useFixedData) {
        // 使用固定的表头结构
        this.tableHeader = [
          { title: '罪名' },
          { title: '2023年数量' },
          { title: '2024年数量' },
          { title: '2024年1-5月数量' },
          { title: '2025年1-5月数量' },
          { title: '2024年-2023年增长数' },
          { title: '2024年-2023年增长率' },
          { title: '2025年1-5月增长数' },
          { title: '2025年1-5月增长率' }
        ];
        this.columns = [...this.tableHeader];
        return;
      }

      // 原有的API调用逻辑
      const { data: tableHeader } = await getTableHeader();
      tableHeader.push({ title: '案件材料是否齐全'})

      const qualityRow1 = {title:'案件质量评查等次', subTitle: [
          { title: '优秀' },
          { title: '合格' },
          { title: '瑕疵' },
          { title: '不合格' }
        ]};
      tableHeader.push(qualityRow1)
      tableHeader.push({title:"巡查案件数量"})
      tableHeader.push({title:"省平台规则预警"})
      tableHeader.push({title:"本院规则预警"})
      tableHeader.push({title:"修改完成数/总预警数"})

      this.tableHeader = tableHeader;
      this.columns = [];
      this.tableHeader.forEach(item => {
        if (item.subTitle && item.subTitle.length) {
          item.subTitle.forEach(subItem => {
            this.columns.push({
              ...subItem,
              title: item.title + "-" + subItem.title
            });
          });
        } else {
          this.columns.push({
            ...item
          });
        }
      });
    },
    async getList() {
      this.loading = true;

      if (this.useFixedData) {
        // 使用固定的犯罪数据
        this.tableData = [...this.crimeStatisticsData];
        this.loading = false;
        return;
      }

      // 原有的API调用逻辑
      let departmentAndUserList = []
      try {
        const { data } = await getDepartmentAndUser();
        departmentAndUserList = data
      } catch (error) {
        this.$message.error("获取部门人员时发送错误")
        return
      }

      this.departmentAndUserList = departmentAndUserList
      let queryData = {};
      let cbrpcjgtjData = {};
      // 时间段
      if (this.searchForm.timeType === 1) {
        queryData = {
          startTime: this.searchForm.singleMonth || '199001',
          endTime: this.searchForm.singleMonth || '209912'
        };
        cbrpcjgtjData = {
          dwbm: window.globalConfig.qualityEvaluation.dwbm,
          wckssj: this.searchForm.singleMonth ? formatStartTime(this.searchForm.singleMonth) : "1990-01-01",
          wcjzsj: this.searchForm.singleMonth ? formatEndTime(this.searchForm.singleMonth): "2099-12-31"
        }
      } else {
        queryData = {
          startTime: this.searchForm.monthrange != null ? this.searchForm.monthrange[0] : '199001',
          endTime: this.searchForm.monthrange != null ? this.searchForm.monthrange[1] : '209912'
        };
        cbrpcjgtjData = {
          dwbm: window.globalConfig.qualityEvaluation.dwbm,
          wckssj: this.searchForm.monthrange != null ? formatStartTime(this.searchForm.monthrange[0]) : "1990-01-01",
          wcjzsj: this.searchForm.monthrange != null ? formatEndTime(this.searchForm.monthrange[1]): "2099-12-31"
        }
      }
      try {
        const leaderData = await getDetailTableLeader(queryData)
        this.yldData = leaderData.data
      } catch (error) {
        console.log(error)
      }
      const { data: statisticsData } = await getStatisticsData(queryData);
      let pjdcData = []
      try {
        const { data } = await listCbrpcjgtj(cbrpcjgtjData)
        // 处理 data
        pjdcData = data
      } catch (error) {
        pjdcData = []
      }
      const { warningData } = await warningDepartmentAndUser(queryData)
      for (const statisticsDataItem in statisticsData) {
        //todo 受理的案件材料是否齐全
        statisticsData[statisticsDataItem] = statisticsData[statisticsDataItem].concat(['0'])
        let find = pjdcData.find(d => d.cbrgh == statisticsDataItem)
        if (find) {
          statisticsData[statisticsDataItem] = statisticsData[statisticsDataItem].concat([
            find.yz.toString(),
            find.hg.toString(),
            find.xc.toString(),
            find.bhg.toString()
          ])
        }else {
          statisticsData[statisticsDataItem] = statisticsData[statisticsDataItem].concat(['0', '0', '0', '0'])
        }

        if (warningData[statisticsDataItem] != null && warningData[statisticsDataItem].length === 4) {
          statisticsData[statisticsDataItem] = statisticsData[statisticsDataItem].concat(warningData[statisticsDataItem])
        } else {
          statisticsData[statisticsDataItem] = statisticsData[statisticsDataItem].concat(["0", "0", "0","0/0"])
        }

      }
      this.statisticsData = statisticsData
      this.tableData = [];
      departmentAndUserList.forEach(row => {
        row.personList.forEach(person => {
          const obj = {
            department: row.name,
            cbr: person.cbr,
            cbrgh: person.cbrgh
          };
          // 拿到统计数据
          const statisticsDataItem = statisticsData[person.cbrgh];
          // 遍历字段进行赋值
          this.columns.forEach((column, idx) => {
            obj[column.title] = statisticsDataItem[idx];
          });
          this.tableData.push(obj);
        });
      });

      // 添加总计行
      const totalItem = statisticsData["total"];
      const totalObj = {
        department: "总计",
        cbr: "",
        cbrgh: ""
      };
      this.columns.forEach((column, idx) => {
        totalObj[column.title] = totalItem[idx];
      });
      this.tableData.push(totalObj);

      this.loading = false;
    },
    getMaxAndMin(prop) {
      const values = this.tableData.filter(i => i.department !== '总计' ).map(item => Number(item[prop]));
      const filter = values.filter(i => i !== 0)
      return {
        max: Math.max(...filter),
        min: Math.min(...filter)
      };
    },
    isMax(value, prop) {
      let max = this.getMaxAndMin(prop).max
      return value == max;
    },
    isMin(value, prop) {
      let min = this.getMaxAndMin(prop).min
      return value == min;
    },
    handleTimeTypeChange() {
      // 切换时间类型时设置为今年的默认值
      const currentYear = new Date().getFullYear().toString()
      if (this.searchForm.timeType === 1) {
        this.searchForm.monthrange = [currentYear + "01", currentYear + "12"];
      } else {
        this.searchForm.singleMonth = currentYear + "12";
      }
      this.getList()
    },
    handleSearch() {
      // 实现查询逻辑
      this.getList();
    },
    handleReset() {
      // 重置为今年的默认值
      const currentYear = new Date().getFullYear().toString()
      this.searchForm = {
        timeType: 2, // 默认使用时间段显示整年
        singleMonth: currentYear + '12',
        monthrange: [currentYear + "01", currentYear + "12"]
      };
      this.tableCellStyleType = {
        max: 0,
        min: 0,
        zero: 0
      }
      this.getList();
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (this.useFixedData) {
        // 对于犯罪数据，不需要合并单元格
        return { rowspan: 1, colspan: 1 };
      }

      // 原有的合并逻辑
      // 处理合计行
      if (rowIndex === this.tableData.length) {
        if (columnIndex === 0) {
          return { rowspan: 1, colspan: 2 };
        }
        if (columnIndex === 1) {
          return { rowspan: 0, colspan: 0 };
        }
      }

      // 原有的部门合并逻辑
      if (columnIndex === 0) {
        const dept = this.tableData[rowIndex].department;
        let count = 1;

        if (rowIndex > 0 && dept === this.tableData[rowIndex - 1].department) {
          return { rowspan: 0, colspan: 0 };
        }

        for (let i = rowIndex + 1; i < this.tableData.length; i++) {
          if (this.tableData[i].department === dept) {
            count++;
          } else {
            break;
          }
        }

        return { rowspan: count, colspan: 1 };
      }
    },
    handleCurrentChange(page) {
      this.listQuery.page = page;
      this.getDetailTableData();
    },
    handleSizeChange(size) {
      this.listQuery.rows = size;
      this.getDetailTableData();
    },
    ruleHandleCurrentChange(page) {
      this.ruleListQuery.pageNo = page;
      this.getRuleTableData();
    },
    ruleHandleSizeChange(size) {
      this.ruleListQuery.rows = size;
      this.getRuleTableData();
    },
    ruleReset() {
      this.ruleListQuery.gzmc = ''
      this.ruleListQuery.pageNo = 1
      this.getRuleTableData()
    },
    getRuleTableData() {
      rulePageList(this.ruleListQuery).then(res=>{
        this.ruleTotal = res.total
        this.ruleTableData = res.rows
      })
    },
    async getDetailTableAllData() {
      const query = this.listQuery
      query.rows = 999999
      const allData = await getDetailTableData(
          this.activeName,
          query
      );
      this.detailTableAllData = allData.data.list;
    },

    async getDetailTableData() {
      this.detailLoading = true;
      const { data } = await getDetailTableData(
          this.activeName,
          this.listQuery
      );
      this.detailTableData = data.list;
      this.total = data.total;
      this.detailLoading = false;
    },
    WarningCloseDialog() {
      this.WarningDialogVisible = false;
      this.WarningActiveName = "";
      this.rowData = {};
    },
    allWarningCloseDialog() {
      this.allWarningDialogVisible = false;
      this.allWarningActiveName = "";
      this.allWarningrowData = {};
    },
    pjdcCloseDialog() {
      this.pjdcDialogVisible = false;
      this.pjdcActiveName = "";
      this.pjdcRowData = {};
    },
    showRuleDialog(type) {
      this.ruleDialogVisible = true;
      this.ruleListQuery.page = 1;
      this.ruleListQuery.type = type;
      this.getRuleTableData()
    },
    ruleCloseDialog() {
      this.ruleDialogVisible = false;
    },

    // 处理预警数量点击事件
    handleWarningCountClick(row) {
      console.log('点击了预警数量:', row);
      // 打开预警内容列表弹框
      this.warningContentDialogVisible = true;
      this.currentWarningRule = row;
      this.warningContentQuery.pageNo = 1;
      this.getWarningContentList();
    },

    // 获取预警内容列表
    async getWarningContentList() {
      this.warningContentLoading = true;
      try {
        // TODO: 替换为实际的API调用
        const response = await warningPageList({
          filters: JSON.stringify({
            warningRuleTitle:this.currentWarningRule.gzmc
          }),
          sort: 'state',
          order: 'ASC',
          isAll: 'true',
          pageNo: this.warningContentQuery.pageNo,
          pageSize: this.warningContentQuery.pageSize
        });

        this.warningContentList = response.rows;
        this.warningContentTotal = response.total;
      } catch (error) {
        console.error('获取预警内容列表失败:', error);
        this.$message.error('获取预警内容列表失败');
      } finally {
        this.warningContentLoading = false;
      }
    },

    // 关闭预警内容弹框
    closeWarningContentDialog() {
      this.warningContentDialogVisible = false;
      this.currentWarningRule = {};
      this.warningContentList = [];
      this.warningContentTotal = 0;
      this.expandedWarningRows = []; // 清理展开状态
    },

    // 预警内容分页处理
    handleWarningContentCurrentChange(page) {
      this.warningContentQuery.pageNo = page;
      this.getWarningContentList();
    },

    handleWarningContentSizeChange(size) {
      this.warningContentQuery.pageSize = size;
      this.warningContentQuery.pageNo = 1;
      this.getWarningContentList();
    },

    // 获取状态文本
    getWarningStateText(state) {
      switch(state) {
        case '0': return '待处理';
        case '1': return '待核查';
        case '2': return '已纠正';
        case '3': return '已归档';
        case '4': return '误报';
        default: return '-';
      }
    },

    // 获取预警类型文本
    getWarningTypeText(type) {
      return type === '0' ? '提醒' : '预警';
    },

    // 生成预警内容表格的唯一行键
    getWarningRowKey(row) {
      return row.id || `warning_${Math.random().toString(36).substr(2, 9)}`;
    },

    // 处理预警内容表格行点击事件
    handleWarningRowClick(row, column, event) {
      // 如果点击的是操作列或展开列，不触发展开
      if (column && (column.type === 'expand' || column.label === '操作')) {
        return;
      }

      // 切换展开状态
      const rowKey = this.getWarningRowKey(row);
      const index = this.expandedWarningRows.indexOf(rowKey);

      if (index > -1) {
        // 如果已展开，则收起
        this.expandedWarningRows.splice(index, 1);
      } else {
        // 如果未展开，则展开
        this.expandedWarningRows.push(rowKey);
      }
    },

    // 格式化JSON字符串
    formatJson(jsonString) {
      if (!jsonString) return '';
      try {
        const parsed = JSON.parse(jsonString);
        return JSON.stringify(parsed, null, 2);
      } catch (error) {
        return jsonString;
      }
    },
    closeDialog() {
      this.dialogVisible = false;
      this.activeName = "";
      this.detailTableAllData = [];
      this.detailTableData = [];
    },
    handleCellClick(row, column, index) {
      if (this.useFixedData) {
        // 对于固定数据，显示简单的信息
        this.$message.info(`点击了 ${row.罪名} 的 ${column} 数据: ${row[column]}`);
        return;
      }

      if (column.title === '优秀' || column.title === '瑕疵' || column.title === '合格' || column.title === '不合格') {
        let pcjl = '0'
        switch (column.title) {
          case '优秀':
            pcjl = '1'
            break
          case '瑕疵':
            pcjl = '3'
            break
          case '合格':
            pcjl = '2'
            break
          case '不合格':
            pcjl = '4'
            break
        }
        this.pjdcRowData = {
          cbrgh: row.cbrgh,
          dwbm: window.globalConfig.qualityEvaluation.dwbm,
          pcjldm: pcjl
        }
        if (this.searchForm.timeType == 2) {
          this.pjdcRowData.wckssj = this.searchForm.monthrange != null ? formatStartTime(this.searchForm.monthrange[0]) : null;
          this.pjdcRowData.wcjzsj = this.searchForm.monthrange != null ? formatEndTime(this.searchForm.monthrange[1]) : null;
        } else {
          this.pjdcRowData.wckssj = formatStartTime(this.searchForm.singleMonth)
          this.pjdcRowData.wcjzsj = formatEndTime(this.searchForm.singleMonth)
        }
        if (this.pjdcRowData.wckssj == null) {
          this.pjdcRowData.wcjzsj = '1990-01-01'
        }
        if (this.pjdcRowData.wckssj == null) {
          this.pjdcRowData.wcjzsj = '2099-12-31'
        }
        this.pjdcActiveName = '案件质量评查-' + column.title
        this.pjdcDialogVisible = true;

      } else if (column.title === '本院规则预警') {
        this.rowData = { cbrgh: row.cbrgh,startTime: '',endTime:'',alData: row }
        if (this.searchForm.timeType == 2) {
          this.rowData.startTime = this.searchForm.monthrange != null ? this.searchForm.monthrange[0] : null;
          this.rowData.endTime = this.searchForm.monthrange != null ? this.searchForm.monthrange[1] : null;
        } else {
          this.rowData.startTime = this.searchForm.singleMonth;
          this.rowData.endTime = this.searchForm.singleMonth;
        }
        if (this.rowData.startTime == null) {
          this.rowData.endTime = '190001'
        }
        if (this.rowData.startTime == null) {
          this.rowData.endTime = '299901'
        }
        this.WarningActiveName = column.title;
        this.WarningDialogVisible = true;
        this.warningType = '市院'
      }
      else if (column.title === '省平台规则预警') {

        this.rowData = { cbrgh: row.cbrgh,startTime: '',endTime:'',alData: row }
        if (this.searchForm.timeType == 2) {
          this.rowData.startTime = this.searchForm.monthrange != null ? this.searchForm.monthrange[0] : null;
          this.rowData.endTime = this.searchForm.monthrange != null ? this.searchForm.monthrange[1] : null;
        } else {
          this.rowData.startTime = this.searchForm.singleMonth;
          this.rowData.endTime = this.searchForm.singleMonth;
        }
        if (this.rowData.startTime == null) {
          this.rowData.endTime = '190001'
        }
        if (this.rowData.startTime == null) {
          this.rowData.endTime = '299901'
        }
        this.WarningActiveName = column.title;
        this.WarningDialogVisible = true;
        this.warningType = '省院'
      }
      else if (column.title === '巡查案件数量') {
        this.allWarningrowData = { cbrgh: row.cbrgh,startTime: '',endTime:'',alData: row }
        if (this.searchForm.timeType == 2) {
          this.allWarningrowData.startTime = this.searchForm.monthrange != null ? this.searchForm.monthrange[0] : null;
          this.allWarningrowData.endTime = this.searchForm.monthrange != null ? this.searchForm.monthrange[1] : null;
        } else {
          this.allWarningrowData.startTime = this.searchForm.singleMonth;
          this.allWarningrowData.endTime = this.searchForm.singleMonth;
        }
        if (this.allWarningrowData.startTime == null) {
          this.allWarningrowData.endTime = '190001'
        }
        if (this.allWarningrowData.startTime == null) {
          this.allWarningrowData.endTime = '299901'
        }
        this.allWarningActiveName = column.title;
        this.allWarningDialogVisible = true
      } else {
        // 初始化查询参数
        this.listQuery = {
          page: 1,
          rows: 5,
          entity: {
            cbrgh: "",
            startTime: "",
            endTime: ""
          }
        }
        if (row.department == "总计") {
          this.listQuery.entity.cbrgh = ""
        } else {
          this.listQuery.entity.cbrgh = row.cbrgh
        }
        if (this.searchForm.timeType == 2) {
          this.listQuery.entity.startTime = this.searchForm.monthrange != null ? this.searchForm.monthrange[0] : null
          this.listQuery.entity.endTime = this.searchForm.monthrange != null ? this.searchForm.monthrange[1] : null
        } else {
          this.listQuery.entity.startTime = this.searchForm.singleMonth
          this.listQuery.entity.endTime = this.searchForm.singleMonth
        }
        if (this.listQuery.entity.startTime == null) {
          this.listQuery.entity.startTime = '190001'
        }
        if (this.listQuery.entity.endTime == null) {
          this.listQuery.entity.endTime = '299901'
        }
        this.currentCell = {
          row,
          column,
          index,
          value: +row[column.title]
        }
        // 选中第一个 Tabs
        this.activeName = this.currentCell.column.codes[0].code
        this.dialogVisible = true
      }
    },
    //修改列方法
    cellStyle({row, column}) {
      // 基础样式对象
      let style = {}
      // 检查是否是预警案件数量或巡查案件数量列
      if (this.focusedColumn === '预警案件数量') {
        switch (column.label) {
          case '修改完成数/总预警数':
            style.borderBottom = '1px solid #d60000 !important'
            style.borderLeft = '1px solid #d60000 !important'
            style.borderRight = '2px solid #d60000 !important'
            break
          case '本院规则预警':
          case '巡查案件数量':
          case '省平台规则预警':
            style.borderBottom = '1px solid #d60000 !important'
            style.borderLeft = '1px solid #d60000 !important'
            break
        }
      }
      if (this.focusedColumn === '办案绩效') {
        switch (column.label) {
          case '监督立案数':
            style.borderLeft = '1px solid #d60000 !important'
            style.borderBottom = '1px solid #d60000 !important'
            style.borderRight = '1px solid #d60000 !important'
            break
          case '瑕疵':
          case '合格' :
          case '不合格':
          case '优秀':
          case '监督撤案数':
          case '追捕、追诉数量' :
          case '刑事抗诉数（再审抗诉、二审抗诉）':
          case '书面纠正侦查活动违法数':
          case '案件材料是否齐全':
            style.borderRight = '1px solid #d60000 !important'
            style.borderBottom = '1px solid #d60000 !important'
            break
        }
      }

      if (row[column.label] == "0" && row.department != '总计' && this.tableCellStyleType.zero === 1) {
        style.background = '#6CB3B8'
        style.color = '#fff'
      }
      if (this.isMax(row[column.label], column.label) && row.department != '总计' && this.tableCellStyleType.max === 1) {
        style.background = '#568EBD'
        style.color = '#fff'
      }
      if (this.isMin(row[column.label], column.label) && row.department != '总计' && this.tableCellStyleType.min === 1) {
        style.background = '#C8495F'
        style.color = '#fff'
      }

      return style
    },
    // 修改表头样式方法
    getHeaderCellStyle({ column }) {
      // 基础样式
      const style = {
        color: '#fff',
        fontWeight: 'bold',
        background: '#409EFF'
        // background: '#d60000'
      }

      // 为特定表头添加类名，用于 hover 效果和点击判断
      if (column.label === '省平台规则预警' || column.label === '本院规则预警') {
        style.cursor = 'pointer'; // 直接在这里设置手型光标
        // 为了更方便地在 CSS 中应用 hover 效果，我们也可以返回一个类名，但直接设置 cursor 也可以
        // 这里我们直接在 getHeaderCellStyle 中返回 cursor 样式，并结合 CSS 实现背景变色
      }

      if (this.focusedColumn === '预警案件数量') {
        switch (column.label) {
          case '修改完成数/总预警数':
            style.borderTop = '1px solid #d60000 !important'
            style.borderBottom = '1px solid #d60000 !important'
            style.borderLeft = '1px solid #d60000 !important'
            style.borderRight = '2px solid #d60000 !important'
            break
          case '本院规则预警':
          case '巡查案件数量':
          case '省平台规则预警':
            style.borderTop = '1px solid #d60000 !important'
            style.borderBottom = '1px solid #d60000 !important'
            style.borderLeft = '1px solid #d60000 !important'
            break
        }
      }
      if (this.focusedColumn === '办案绩效') {
        console.log(column.label)
        switch (column.label) {
          case '案件质量评查等次':
            style.borderBottom = '1px solid #d60000 !important'
            style.borderTop = '1px solid #d60000 !important'
            style.borderRight = '1px solid #d60000 !important'
            break
          case '监督立案数':
            style.borderLeft = '1px solid #d60000 !important'
            style.borderRight = '1px solid #d60000 !important'
            style.borderBottom = '1px solid #d60000 !important'
            style.borderTop = '1px solid #d60000 !important'
            break
          case '瑕疵':
          case '合格' :
          case '不合格':
          case '优秀':
            style.borderRight = '1px solid #d60000 !important'
            style.borderBottom = '1px solid #d60000 !important'
            break
          case '监督撤案数':
          case '追捕、追诉数量' :
          case '刑事抗诉数（再审抗诉、二审抗诉）':
          case '书面纠正侦查活动违法数':
          case '案件材料是否齐全':
            style.borderRight = '1px solid #d60000 !important'
            style.borderBottom = '1px solid #d60000 !important'
            style.borderTop = '1px solid #d60000 !important'
            break
        }
      }
      return style
    },
    // 返回特定表头的类名
    getHeaderCellClassName({ column }) {
      if (column.label === '省平台规则预警' || column.label === '本院规则预警') {
        return 'rule-header-cell'; // 为目标列返回类名
      }
      return ''; // 其他列返回空字符串
    },
    // 表头点击事件处理
    handleHeaderClick(column) {
      if (column.label === '省平台规则预警') {
        this.showRuleDialog('省院');
      }
      if (column.label === '本院规则预警') {
        this.showRuleDialog('本院');
      }
    },
    // 修改聚焦方法
    focusWarningColumn(event) {
      event.stopPropagation()
      this.focusedColumn = '预警案件数量'

      this.$nextTick(() => {
        const table = this.$refs.dataTable
        const tableEl = table.$el
        const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper')
        const headerWrapper = tableEl.querySelector('.el-table__header-wrapper')

        if (bodyWrapper && headerWrapper) {
          const maxScroll = bodyWrapper.scrollWidth - bodyWrapper.clientWidth
          this.smoothScroll(bodyWrapper, maxScroll)
          this.smoothScroll(headerWrapper, maxScroll)
        }
      })
    },
    focusPerformance(event) {
      event.stopPropagation()
      this.focusedColumn = '办案绩效'

      this.$nextTick(() => {
        const table = this.$refs.dataTable
        const tableEl = table.$el
        const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper')
        const headerWrapper = tableEl.querySelector('.el-table__header-wrapper')

        if (bodyWrapper && headerWrapper) {
          const maxScroll = bodyWrapper.scrollWidth - bodyWrapper.clientWidth
          this.smoothScroll(bodyWrapper, maxScroll)
          this.smoothScroll(headerWrapper, maxScroll)
          // this.smoothScroll(bodyWrapper, 300)
          // this.smoothScroll(headerWrapper, 300)
        }
      })
    },
    caseStatuteOfLimitations(event) {
      event.stopPropagation()
      this.focusedColumn = '案件时效'

      this.$nextTick(() => {
        const table = this.$refs.dataTable
        const tableEl = table.$el
        const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper')
        const headerWrapper = tableEl.querySelector('.el-table__header-wrapper')

        if (bodyWrapper && headerWrapper) {
          const maxScroll = bodyWrapper.scrollWidth - bodyWrapper.clientWidth
          this.smoothScroll(bodyWrapper, 1)
          this.smoothScroll(headerWrapper, 1)
          // this.smoothScroll(bodyWrapper, 300)
          // this.smoothScroll(headerWrapper, 300)
        }
      })
    },
    handleGlobalClick() {
      this.focusedColumn = ''
    },

    // 格式化百分比
    formatPercentage(value) {
      if (value === null || value === undefined || value === '') return '-';
      return value.toFixed(2) + '%';
    },

    // 获取增长数据的样式类
    getGrowthClass(value) {
      if (value === null || value === undefined || value === '') return '';
      const numValue = Number(value);
      if (numValue > 0) return 'growth-positive';
      if (numValue < 0) return 'growth-negative';
      return '';
    },

    // 切换数据源
    toggleDataSource() {
      this.useFixedData = !this.useFixedData;
      this.getList();
    }
  }
};
</script>

<style lang="scss" scoped>
.duration-statistics {
  background-color: #f5f7fa;
  min-height: 100vh;

  .filter-section {
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    //margin-bottom: 16px;

    .filter-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .filter-group {
      display: flex;
      gap: 12px;
      flex: 1;

      .wide-select {
        width: 240px;
      }

      .normal-select {
        width: 120px;
      }

      .time-select {
        display: flex;
        align-items: center;
        gap: 12px;

        .date-picker {
          width: 240px;
        }
      }
    }

    .data-type-switch {
      display: flex;
      gap: 16px;

      .switch-item {
        cursor: pointer;
        padding: 6px 12px;
        border-radius: 4px;
        color: #606266;

        &.active {
          background-color: #e6f2ff;
          color: #409eff;
        }

        i {
          margin-right: 4px;
        }
      }
    }

    .analysis-options {
      display: flex;
      gap: 24px;
      align-items: center;

      .option-item {
        cursor: pointer;
        color: #606266;

        &.active {
          color: #409eff;
        }

        i {
          margin-right: 4px;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      align-items: center;

      .search-input {
        width: 200px;
      }

      .el-button {
        padding: 8px 16px;
      }
    }
  }

  .table-section {
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;

    .table-title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;


      .title-decoration {
        width: 4px;
        height: 20px;
        background-color: #409eff;
        margin-right: 8px;
        border-radius: 2px;
      }

      h2 {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
        margin: 0;
      }
    }
  }

  .notes-area {
    margin-top: 16px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    line-height: 24px;
    font-size: 16px;
    color: #4f5e7b;
  }
}

.cell-content {
  display: inline-block;
  width: 100%;
  cursor: pointer;

  //.max-num {
  //  //color: #2487fa;
  //  background: #2487fa;
  //  color: #fff;
  //}

  //.max-num:hover {
  //  font-weight: bold;
  //}


  //.min-num {
  //  //color: red;
  //  background: orange;
  //  color: #fff;
  //}

  //.min-num:hover {
  //  font-weight: bold;
  //}

}
.calculation-rule {
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .rule-label {
    color: #606266;
    font-weight: 500;
  }

  .rule-content {
    color: #303133;
    line-height: 1.5;
  }
}

// 为特定表头添加 hover 效果
::v-deep .el-table__header-wrapper th {
  &.is-leaf { // 只针对最底层的表头单元格
    div.cell:hover {
      // 检查列的 property 是否匹配
      // 注意：这种方式依赖于 Element UI 内部结构，可能不够稳定
      // 如果 getHeaderCellStyle 能稳定添加 class 会更好，但目前先用这种方式
    }
  }
}

// 通过 getHeaderCellStyle 添加的 cursor 样式已经生效
// 这里我们只添加 hover 时的背景色变化
// 使用 getHeaderCellClassName 添加的类名来定位
::v-deep .el-table__header-wrapper th.rule-header-cell {
  &:hover {
    background-color: #66b1ff !important; // 悬停时改变背景色
  }
}

/* 预警数量单元格样式 */
.warning-count-cell {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  display: inline-block;
  width: 100%;
  text-align: center;
}

.warning-count-cell:hover {
  background-color: #e6f3ff !important; // 浅蓝色背景
  color: #409eff;
}

/* 预警内容展开样式 */
.warning-expand-content {
  padding-right: 20px;
  padding-left: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  margin: 10px;
}

.warning-expand-content h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.warning-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
  align-items: start;
}

.detail-item {
  display: flex;
  flex-direction: column;
  background: white;
  padding: 5px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  font-size: 12px;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item span {
  color: #303133;
  font-size: 14px;
  word-break: break-word;
}

/* JSON行样式 */
.json-row {
  grid-column: 1 / -1;
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.json-item {
  flex: 1;
  margin: 0;
}

.json-content {
  margin-top: 8px;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  overflow: visible; /* 去除滚动条 */
  max-height: none; /* 去除高度限制 */
}

.json-content pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 预警内容表格行点击样式 */
.warning-expand-content + .el-table tbody tr {
  cursor: pointer;
}

.warning-expand-content + .el-table tbody tr:hover {
  background-color: #f5f7fa !important;
}

/* 增长数据样式 */
.growth-positive {
  color: #67C23A;
  font-weight: bold;
}

.growth-negative {
  color: #F56C6C;
  font-weight: bold;
}

.cell-content {
  cursor: pointer;
  &:hover {
    color: #409EFF;
  }
}

</style>
