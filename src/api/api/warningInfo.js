/*
 * @Description:
 */
import request from '@/utils/request'
import fileRequest from "@/utils/fileRequest"
const baseURL = window.globalConfig.warningInfo.warningApi


// 获取部门和员额
export function warningDepartmentAndUser(data) {
    return request({
        baseURL,
        url: '/person/data',
        method: 'POST',
        data: data
    })
}

// 获取部门和员额
export function warningPageList(param) {
    return request({
        baseURL,
        url: '/listByPage',
        method: 'POST',
        data: param
    })
}

// 获取部门和员额
export function rulePageList(param) {
    return request({
        baseURL,
        url: '/rule/list',
        method: 'POST',
        data: param
    })
}

// 获取部门和员额
export function warningGroupRule(param) {
    return request({
        baseURL,
        url: '/listByPage/groupRule',
        method: 'GET',
        params: param
    })
}
// 获取部门和员额
export function warningCasePage(param) {
    return request({
        baseURL,
        url: '/case/page',
        method: 'POST',
        data: param
    })
}

// 获取部门和员额
export function warninglistByFilter(param) {
    return request({
        baseURL,
        url: '/listByFilter',
        method: 'GET',
        params: param
    })
}


// 获取部门和员额
export function warningCaseDetail(data) {
    return request({
        baseURL,
        url: '/case/detail',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
