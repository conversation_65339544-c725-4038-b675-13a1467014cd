<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>犯罪态势分析表格测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
        }
        
        .table-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .table-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .title-decoration {
            width: 4px;
            height: 24px;
            background: #409EFF;
            margin-right: 10px;
        }
        
        h2 {
            margin: 0;
            color: #303133;
            font-size: 18px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        th, td {
            border: 1px solid #EBEEF5;
            padding: 12px 8px;
            text-align: center;
            font-size: 14px;
        }
        
        th {
            background-color: #409EFF;
            color: white;
            font-weight: bold;
        }
        
        .crime-name {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: left;
            padding-left: 15px;
        }
        
        .growth-positive {
            color: #67C23A;
            font-weight: bold;
        }
        
        .growth-negative {
            color: #F56C6C;
            font-weight: bold;
        }
        
        .cell-content {
            cursor: pointer;
        }
        
        .cell-content:hover {
            color: #409EFF;
        }
        
        .toggle-btn {
            background: #E6A23C;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        .toggle-btn:hover {
            background: #CF9236;
        }
    </style>
</head>
<body>
    <div class="table-container">
        <div class="table-title">
            <div class="title-decoration"></div>
            <h2>犯罪态势分析</h2>
        </div>
        
        <button class="toggle-btn" onclick="toggleDataSource()">
            切换到API数据
        </button>
        
        <table id="crimeTable">
            <thead>
                <tr>
                    <th>罪名</th>
                    <th>2023年数量</th>
                    <th>2024年数量</th>
                    <th>2024年1-5月数量</th>
                    <th>2025年1-5月数量</th>
                    <th>2024年-2023年增长数</th>
                    <th>2024年-2023年增长率</th>
                    <th>2025年1-5月增长数</th>
                    <th>2025年1-5月增长率</th>
                </tr>
            </thead>
            <tbody id="tableBody">
                <!-- 数据将通过JavaScript填充 -->
            </tbody>
        </table>
    </div>

    <script>
        // 固定的犯罪数据
        const crimeStatisticsData = [
            {
                "罪名": "危险驾驶罪",
                "2023年数量": 307,
                "2024年数量": 233,
                "2024年1-5月数量": 92,
                "2025年1-5月数量": 102,
                "2024年-2023年增长数": -74,
                "2024年-2023年增长率": -24.10423,
                "2025年1-5月增长数": 10,
                "2025年1-5月增长率": 10.86957
            },
            {
                "罪名": "盗窃罪",
                "2023年数量": 137,
                "2024年数量": 164,
                "2024年1-5月数量": 80,
                "2025年1-5月数量": 66,
                "2024年-2023年增长数": 27,
                "2024年-2023年增长率": 19.70803,
                "2025年1-5月增长数": -14,
                "2025年1-5月增长率": -17.5
            },
            {
                "罪名": "诈骗罪",
                "2023年数量": 112,
                "2024年数量": 120,
                "2024年1-5月数量": 61,
                "2025年1-5月数量": 49,
                "2024年-2023年增长数": 8,
                "2024年-2023年增长率": 7.14286,
                "2025年1-5月增长数": -12,
                "2025年1-5月增长率": -19.67213
            }
        ];

        let useFixedData = true;

        // 格式化百分比
        function formatPercentage(value) {
            if (value === null || value === undefined || value === '') return '-';
            return value.toFixed(2) + '%';
        }

        // 获取增长数据的样式类
        function getGrowthClass(value) {
            if (value === null || value === undefined || value === '') return '';
            const numValue = Number(value);
            if (numValue > 0) return 'growth-positive';
            if (numValue < 0) return 'growth-negative';
            return '';
        }

        // 渲染表格
        function renderTable() {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';

            if (useFixedData) {
                crimeStatisticsData.forEach(row => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td class="crime-name">${row['罪名']}</td>
                        <td class="cell-content" onclick="handleCellClick('${row['罪名']}', '2023年数量', ${row['2023年数量']})">${row['2023年数量'] || '-'}</td>
                        <td class="cell-content" onclick="handleCellClick('${row['罪名']}', '2024年数量', ${row['2024年数量']})">${row['2024年数量'] || '-'}</td>
                        <td class="cell-content" onclick="handleCellClick('${row['罪名']}', '2024年1-5月数量', ${row['2024年1-5月数量']})">${row['2024年1-5月数量'] || '-'}</td>
                        <td class="cell-content" onclick="handleCellClick('${row['罪名']}', '2025年1-5月数量', ${row['2025年1-5月数量']})">${row['2025年1-5月数量'] || '-'}</td>
                        <td class="cell-content ${getGrowthClass(row['2024年-2023年增长数'])}" onclick="handleCellClick('${row['罪名']}', '2024年-2023年增长数', ${row['2024年-2023年增长数']})">${row['2024年-2023年增长数'] || '-'}</td>
                        <td class="cell-content ${getGrowthClass(row['2024年-2023年增长率'])}" onclick="handleCellClick('${row['罪名']}', '2024年-2023年增长率', ${row['2024年-2023年增长率']})">${formatPercentage(row['2024年-2023年增长率'])}</td>
                        <td class="cell-content ${getGrowthClass(row['2025年1-5月增长数'])}" onclick="handleCellClick('${row['罪名']}', '2025年1-5月增长数', ${row['2025年1-5月增长数']})">${row['2025年1-5月增长数'] || '-'}</td>
                        <td class="cell-content ${getGrowthClass(row['2025年1-5月增长率'])}" onclick="handleCellClick('${row['罪名']}', '2025年1-5月增长率', ${row['2025年1-5月增长率']})">${formatPercentage(row['2025年1-5月增长率'])}</td>
                    `;
                    tbody.appendChild(tr);
                });
            } else {
                // 模拟API数据为空的情况
                const tr = document.createElement('tr');
                tr.innerHTML = '<td colspan="9" style="text-align: center; color: #909399;">暂无API数据</td>';
                tbody.appendChild(tr);
            }
        }

        // 切换数据源
        function toggleDataSource() {
            useFixedData = !useFixedData;
            const btn = document.querySelector('.toggle-btn');
            btn.textContent = useFixedData ? '切换到API数据' : '切换到固定数据';
            renderTable();
        }

        // 处理单元格点击
        function handleCellClick(crimeName, column, value) {
            alert(`点击了 ${crimeName} 的 ${column} 数据: ${value}`);
        }

        // 初始化表格
        renderTable();
    </script>
</body>
</html>
